import { apiConfig, endpoints } from '@/config/api'
import { useAuthStore } from '@/stores/auth'
import { useTokenStorage } from '@/composables/useTokenStorage'

export interface ApiResponse<T = any> {
  data: T
  message?: string
  success: boolean
}

export interface ApiError {
  message: string
  code?: string
  status?: number
}

class ApiService {
  private baseURL: string
  private timeout: number
  private defaultHeaders: Record<string, string>

  constructor() {
    this.baseURL = apiConfig.baseURL
    this.timeout = apiConfig.timeout
    this.defaultHeaders = apiConfig.headers
  }

  private async getAuthHeaders(): Promise<Record<string, string>> {
    const { getAccessToken } = useTokenStorage()
    const token = getAccessToken()
    
    if (token) {
      return {
        'Authorization': `Bearer ${token}`,
      }
    }
    
    return {}
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const authHeaders = await this.getAuthHeaders()
    
    const config: RequestInit = {
      ...options,
      headers: {
        ...this.defaultHeaders,
        ...authHeaders,
        ...options.headers,
      },
    }

    // Add timeout
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), this.timeout)
    config.signal = controller.signal

    try {
      const response = await fetch(`${this.baseURL}${endpoint}`, config)
      clearTimeout(timeoutId)

      if (!response.ok) {
        await this.handleErrorResponse(response)
      }

      const data = await response.json()
      return {
        data,
        success: true,
        message: data.message,
      }
    } catch (error) {
      clearTimeout(timeoutId)
      throw this.handleError(error)
    }
  }

  private async handleErrorResponse(response: Response): Promise<never> {
    let errorMessage = 'An error occurred'
    let errorCode = 'UNKNOWN_ERROR'

    try {
      const errorData = await response.json()
      errorMessage = errorData.message || errorMessage
      errorCode = errorData.code || errorCode
    } catch {
      // If we can't parse the error response, use the status text
      errorMessage = response.statusText || errorMessage
    }

    // Handle specific HTTP status codes
    switch (response.status) {
      case 401:
        // Unauthorized - token might be expired
        await this.handleUnauthorized()
        errorMessage = 'Authentication required'
        break
      case 403:
        errorMessage = 'Access forbidden'
        break
      case 404:
        errorMessage = 'Resource not found'
        break
      case 422:
        errorMessage = 'Validation error'
        break
      case 500:
        errorMessage = 'Internal server error'
        break
    }

    const error: ApiError = {
      message: errorMessage,
      code: errorCode,
      status: response.status,
    }

    throw error
  }

  private handleError(error: any): ApiError {
    if (error.name === 'AbortError') {
      return {
        message: 'Request timeout',
        code: 'TIMEOUT_ERROR',
      }
    }

    if (error instanceof TypeError && error.message.includes('fetch')) {
      return {
        message: 'Network error - please check your connection',
        code: 'NETWORK_ERROR',
      }
    }

    // If it's already an ApiError, return it as is
    if (error.message && typeof error.message === 'string') {
      return error
    }

    return {
      message: 'An unexpected error occurred',
      code: 'UNKNOWN_ERROR',
    }
  }

  private async handleUnauthorized(): Promise<void> {
    const authStore = useAuthStore()
    
    try {
      // Try to refresh the token
      await authStore.refreshAuthToken()
    } catch (refreshError) {
      // If refresh fails, logout the user
      await authStore.logout()
      
      // Redirect to login page
      if (typeof window !== 'undefined') {
        window.location.href = '/auth/signin'
      }
    }
  }

  // HTTP Methods
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
    let url = endpoint
    
    if (params) {
      const searchParams = new URLSearchParams()
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value))
        }
      })
      url += `?${searchParams.toString()}`
    }

    return this.request<T>(url, {
      method: 'GET',
    })
  }

  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async patch<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'DELETE',
    })
  }

  // File upload
  async upload<T>(endpoint: string, file: File, additionalData?: Record<string, any>): Promise<ApiResponse<T>> {
    const formData = new FormData()
    formData.append('file', file)
    
    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, String(value))
      })
    }

    const authHeaders = await this.getAuthHeaders()

    return this.request<T>(endpoint, {
      method: 'POST',
      body: formData,
      headers: {
        ...authHeaders,
        // Don't set Content-Type for FormData, let the browser set it
      },
    })
  }
}

// Create and export a singleton instance
export const apiService = new ApiService()

// Convenience methods for common endpoints
export const authApi = {
  getProfile: () => apiService.get(endpoints.auth.profile),
  refreshToken: () => apiService.post(endpoints.auth.refresh),
}

export const userApi = {
  getProfile: () => apiService.get(endpoints.user.profile),
  updateProfile: (data: any) => apiService.put(endpoints.user.update, data),
}

export default apiService
