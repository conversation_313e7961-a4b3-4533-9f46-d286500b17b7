<template>
  <div class="flex-1 flex flex-col pt-5 pb-4 overflow-y-auto">
    <!-- Logo -->
    <div class="flex items-center flex-shrink-0 px-4">
      <img
        class="h-8 w-auto"
        src="/favicon.ico"
        alt="Your Company"
      />
      <span class="ml-2 text-xl font-semibold text-gray-900">Analyst Web</span>
    </div>
    
    <!-- Navigation -->
    <nav class="mt-8 flex-1 px-2 space-y-1">
      <router-link
        v-for="item in navigation"
        :key="item.name"
        :to="item.href"
        :class="[
          isCurrentRoute(item.href)
            ? 'bg-primary-100 border-primary-500 text-primary-700'
            : 'border-transparent text-gray-600 hover:bg-gray-50 hover:text-gray-900',
          'group flex items-center px-3 py-2 text-sm font-medium border-l-4 transition-colors duration-200'
        ]"
      >
        <component
          :is="item.icon"
          :class="[
            isCurrentRoute(item.href)
              ? 'text-primary-500'
              : 'text-gray-400 group-hover:text-gray-500',
            'mr-3 flex-shrink-0 h-6 w-6'
          ]"
        />
        {{ item.name }}
      </router-link>
    </nav>
  </div>
  
  <!-- User info at bottom -->
  <div class="flex-shrink-0 flex border-t border-gray-200 p-4">
    <div class="flex items-center">
      <div>
        <img
          class="inline-block h-9 w-9 rounded-full"
          :src="authStore.userAvatar || defaultAvatar"
          :alt="authStore.userDisplayName"
        />
      </div>
      <div class="ml-3">
        <p class="text-sm font-medium text-gray-700 group-hover:text-gray-900">
          {{ authStore.userDisplayName }}
        </p>
        <p class="text-xs font-medium text-gray-500 group-hover:text-gray-700">
          {{ authStore.user?.email }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import {
  HomeIcon,
  UserIcon,
  ChartBarIcon,
  CogIcon,
  DocumentTextIcon,
} from '@heroicons/vue/24/outline'

const route = useRoute()
const authStore = useAuthStore()

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },
  { name: 'Analytics', href: '/analytics', icon: ChartBarIcon },
  { name: 'Reports', href: '/reports', icon: DocumentTextIcon },
  { name: 'Profile', href: '/profile', icon: UserIcon },
  { name: 'Settings', href: '/settings', icon: CogIcon },
]

const defaultAvatar = 'https://ui-avatars.com/api/?name=' + encodeURIComponent(authStore.userDisplayName || 'User') + '&background=3b82f6&color=fff'

const isCurrentRoute = (href: string) => {
  return route.path === href || route.path.startsWith(href + '/')
}
</script>
