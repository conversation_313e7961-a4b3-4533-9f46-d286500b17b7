<template>
  <div :class="cardClasses">
    <!-- Header -->
    <div
      v-if="$slots.header || title"
      :class="headerClasses"
    >
      <slot name="header">
        <h3 v-if="title" class="text-lg font-semibold text-gray-900">
          {{ title }}
        </h3>
      </slot>
    </div>
    
    <!-- Body -->
    <div :class="bodyClasses">
      <slot />
    </div>
    
    <!-- Footer -->
    <div
      v-if="$slots.footer"
      :class="footerClasses"
    >
      <slot name="footer" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  title?: string
  variant?: 'default' | 'outlined' | 'elevated' | 'filled'
  padding?: 'none' | 'sm' | 'md' | 'lg'
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  shadow?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  hover?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  padding: 'md',
  rounded: 'lg',
  shadow: 'md',
  hover: false,
})

const baseClasses = 'bg-white transition-all duration-200'

const variantClasses = computed(() => {
  switch (props.variant) {
    case 'outlined':
      return 'border border-gray-200'
    case 'elevated':
      return 'border border-gray-100'
    case 'filled':
      return 'bg-gray-50 border border-gray-200'
    case 'default':
    default:
      return 'border border-gray-200'
  }
})

const roundedClasses = computed(() => {
  switch (props.rounded) {
    case 'none':
      return 'rounded-none'
    case 'sm':
      return 'rounded-sm'
    case 'md':
      return 'rounded-md'
    case 'lg':
      return 'rounded-lg'
    case 'xl':
      return 'rounded-xl'
    default:
      return 'rounded-lg'
  }
})

const shadowClasses = computed(() => {
  switch (props.shadow) {
    case 'none':
      return 'shadow-none'
    case 'sm':
      return 'shadow-sm'
    case 'md':
      return 'shadow-md'
    case 'lg':
      return 'shadow-lg'
    case 'xl':
      return 'shadow-xl'
    default:
      return 'shadow-md'
  }
})

const hoverClasses = computed(() => {
  return props.hover ? 'hover:shadow-lg hover:-translate-y-1' : ''
})

const cardClasses = computed(() => {
  return [
    baseClasses,
    variantClasses.value,
    roundedClasses.value,
    shadowClasses.value,
    hoverClasses.value,
  ].join(' ')
})

const paddingClasses = computed(() => {
  switch (props.padding) {
    case 'none':
      return ''
    case 'sm':
      return 'p-4'
    case 'md':
      return 'p-6'
    case 'lg':
      return 'p-8'
    default:
      return 'p-6'
  }
})

const headerClasses = computed(() => {
  const base = 'border-b border-gray-200'
  return props.padding === 'none' ? `${base} p-6 pb-4` : `${base} ${paddingClasses.value} pb-4`
})

const bodyClasses = computed(() => {
  return props.padding === 'none' ? 'p-6' : paddingClasses.value
})

const footerClasses = computed(() => {
  const base = 'border-t border-gray-200 bg-gray-50'
  return props.padding === 'none' ? `${base} p-6 pt-4` : `${base} ${paddingClasses.value} pt-4`
})
</script>
