import Cookies from 'js-cookie'
import type { TokenData } from '@/types/auth'

const TOKEN_KEY = 'auth_tokens'
const ACCESS_TOKEN_KEY = 'access_token'
const REFRESH_TOKEN_KEY = 'refresh_token'

export const useTokenStorage = () => {
  // Save tokens securely
  const saveTokens = (tokenData: TokenData) => {
    try {
      // Store in httpOnly cookies for better security (if your backend supports it)
      // For now, we'll use secure cookies with appropriate flags
      const cookieOptions = {
        secure: import.meta.env.PROD, // Only use secure in production
        sameSite: 'strict' as const,
        expires: 7, // 7 days
      }

      // Store access token (shorter expiry)
      Cookies.set(ACCESS_TOKEN_KEY, tokenData.accessToken, {
        ...cookieOptions,
        expires: 1, // 1 day
      })

      // Store refresh token (longer expiry)
      Cookies.set(REFRESH_TOKEN_KEY, tokenData.refreshToken, cookieOptions)

      // Also store in localStorage as backup (encrypted in production)
      const tokenString = JSON.stringify({
        accessToken: tokenData.accessToken,
        refreshToken: tokenData.refreshToken,
        expiresIn: tokenData.expiresIn,
        tokenType: tokenData.tokenType,
        timestamp: Date.now(),
      })

      localStorage.setItem(TOKEN_KEY, tokenString)
    } catch (error) {
      console.error('Error saving tokens:', error)
    }
  }

  // Get tokens
  const getTokens = (): TokenData | null => {
    try {
      // Try to get from cookies first
      const accessToken = Cookies.get(ACCESS_TOKEN_KEY)
      const refreshToken = Cookies.get(REFRESH_TOKEN_KEY)

      if (accessToken && refreshToken) {
        return {
          accessToken,
          refreshToken,
          expiresIn: 3600,
          tokenType: 'Bearer',
        }
      }

      // Fallback to localStorage
      const tokenString = localStorage.getItem(TOKEN_KEY)
      if (tokenString) {
        const tokenData = JSON.parse(tokenString)
        
        // Check if token is not expired (basic check)
        const now = Date.now()
        const tokenAge = now - tokenData.timestamp
        const maxAge = 24 * 60 * 60 * 1000 // 24 hours

        if (tokenAge < maxAge) {
          return {
            accessToken: tokenData.accessToken,
            refreshToken: tokenData.refreshToken,
            expiresIn: tokenData.expiresIn,
            tokenType: tokenData.tokenType,
          }
        } else {
          // Token expired, clear it
          clearTokens()
        }
      }

      return null
    } catch (error) {
      console.error('Error getting tokens:', error)
      return null
    }
  }

  // Clear tokens
  const clearTokens = () => {
    try {
      // Remove from cookies
      Cookies.remove(ACCESS_TOKEN_KEY)
      Cookies.remove(REFRESH_TOKEN_KEY)

      // Remove from localStorage
      localStorage.removeItem(TOKEN_KEY)
    } catch (error) {
      console.error('Error clearing tokens:', error)
    }
  }

  // Get access token only
  const getAccessToken = (): string | null => {
    const tokens = getTokens()
    return tokens?.accessToken || null
  }

  // Get refresh token only
  const getRefreshToken = (): string | null => {
    const tokens = getTokens()
    return tokens?.refreshToken || null
  }

  // Check if tokens exist
  const hasTokens = (): boolean => {
    return getTokens() !== null
  }

  // Update access token only (for token refresh)
  const updateAccessToken = (newAccessToken: string) => {
    const existingTokens = getTokens()
    if (existingTokens) {
      saveTokens({
        ...existingTokens,
        accessToken: newAccessToken,
      })
    }
  }

  return {
    saveTokens,
    getTokens,
    clearTokens,
    getAccessToken,
    getRefreshToken,
    hasTokens,
    updateAccessToken,
  }
}
