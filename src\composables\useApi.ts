import { ref, computed } from 'vue'
import { apiService, type ApiResponse, type ApiError } from '@/services/api'

export interface UseApiOptions {
  immediate?: boolean
  onSuccess?: (data: any) => void
  onError?: (error: ApiError) => void
}

export function useApi<T = any>(
  apiCall: () => Promise<ApiResponse<T>>,
  options: UseApiOptions = {}
) {
  const { immediate = false, onSuccess, onError } = options

  const data = ref<T | null>(null)
  const error = ref<ApiError | null>(null)
  const isLoading = ref(false)
  const isSuccess = ref(false)
  const isError = ref(false)

  const execute = async (...args: any[]) => {
    try {
      isLoading.value = true
      isError.value = false
      isSuccess.value = false
      error.value = null

      const response = await apiCall()
      data.value = response.data
      isSuccess.value = true

      if (onSuccess) {
        onSuccess(response.data)
      }

      return response
    } catch (err) {
      const apiError = err as ApiError
      error.value = apiError
      isError.value = true

      if (onError) {
        onError(apiError)
      }

      throw apiError
    } finally {
      isLoading.value = false
    }
  }

  const reset = () => {
    data.value = null
    error.value = null
    isLoading.value = false
    isSuccess.value = false
    isError.value = false
  }

  // Execute immediately if requested
  if (immediate) {
    execute()
  }

  return {
    data: computed(() => data.value),
    error: computed(() => error.value),
    isLoading: computed(() => isLoading.value),
    isSuccess: computed(() => isSuccess.value),
    isError: computed(() => isError.value),
    execute,
    reset,
  }
}

// Specific composables for common operations
export function useGet<T>(endpoint: string, params?: Record<string, any>, options?: UseApiOptions) {
  return useApi<T>(() => apiService.get<T>(endpoint, params), options)
}

export function usePost<T>(endpoint: string, options?: UseApiOptions) {
  return useApi<T>((data?: any) => apiService.post<T>(endpoint, data), options)
}

export function usePut<T>(endpoint: string, options?: UseApiOptions) {
  return useApi<T>((data?: any) => apiService.put<T>(endpoint, data), options)
}

export function usePatch<T>(endpoint: string, options?: UseApiOptions) {
  return useApi<T>((data?: any) => apiService.patch<T>(endpoint, data), options)
}

export function useDelete<T>(endpoint: string, options?: UseApiOptions) {
  return useApi<T>(() => apiService.delete<T>(endpoint), options)
}

// Mutation composable for data modification operations
export function useMutation<T = any, TVariables = any>(
  mutationFn: (variables: TVariables) => Promise<ApiResponse<T>>,
  options: UseApiOptions = {}
) {
  const { onSuccess, onError } = options

  const data = ref<T | null>(null)
  const error = ref<ApiError | null>(null)
  const isLoading = ref(false)
  const isSuccess = ref(false)
  const isError = ref(false)

  const mutate = async (variables: TVariables) => {
    try {
      isLoading.value = true
      isError.value = false
      isSuccess.value = false
      error.value = null

      const response = await mutationFn(variables)
      data.value = response.data
      isSuccess.value = true

      if (onSuccess) {
        onSuccess(response.data)
      }

      return response
    } catch (err) {
      const apiError = err as ApiError
      error.value = apiError
      isError.value = true

      if (onError) {
        onError(apiError)
      }

      throw apiError
    } finally {
      isLoading.value = false
    }
  }

  const reset = () => {
    data.value = null
    error.value = null
    isLoading.value = false
    isSuccess.value = false
    isError.value = false
  }

  return {
    data: computed(() => data.value),
    error: computed(() => error.value),
    isLoading: computed(() => isLoading.value),
    isSuccess: computed(() => isSuccess.value),
    isError: computed(() => isError.value),
    mutate,
    reset,
  }
}

// Query composable for data fetching with caching
export function useQuery<T = any>(
  queryKey: string,
  queryFn: () => Promise<ApiResponse<T>>,
  options: UseApiOptions & {
    staleTime?: number
    cacheTime?: number
    refetchOnWindowFocus?: boolean
  } = {}
) {
  const {
    immediate = true,
    staleTime = 5 * 60 * 1000, // 5 minutes
    cacheTime = 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus = true,
    onSuccess,
    onError,
  } = options

  // Simple in-memory cache
  const cache = new Map<string, { data: any; timestamp: number }>()

  const data = ref<T | null>(null)
  const error = ref<ApiError | null>(null)
  const isLoading = ref(false)
  const isSuccess = ref(false)
  const isError = ref(false)
  const isFetching = ref(false)

  const getCachedData = () => {
    const cached = cache.get(queryKey)
    if (cached && Date.now() - cached.timestamp < staleTime) {
      return cached.data
    }
    return null
  }

  const setCachedData = (newData: T) => {
    cache.set(queryKey, { data: newData, timestamp: Date.now() })
  }

  const execute = async (force = false) => {
    // Check cache first
    if (!force) {
      const cachedData = getCachedData()
      if (cachedData) {
        data.value = cachedData
        isSuccess.value = true
        return { data: cachedData, success: true }
      }
    }

    try {
      isFetching.value = true
      if (!data.value) {
        isLoading.value = true
      }
      isError.value = false
      error.value = null

      const response = await queryFn()
      data.value = response.data
      isSuccess.value = true
      setCachedData(response.data)

      if (onSuccess) {
        onSuccess(response.data)
      }

      return response
    } catch (err) {
      const apiError = err as ApiError
      error.value = apiError
      isError.value = true

      if (onError) {
        onError(apiError)
      }

      throw apiError
    } finally {
      isLoading.value = false
      isFetching.value = false
    }
  }

  const refetch = () => execute(true)

  const reset = () => {
    data.value = null
    error.value = null
    isLoading.value = false
    isSuccess.value = false
    isError.value = false
    isFetching.value = false
    cache.delete(queryKey)
  }

  // Execute immediately if requested
  if (immediate) {
    execute()
  }

  // Refetch on window focus
  if (refetchOnWindowFocus && typeof window !== 'undefined') {
    const handleFocus = () => {
      if (data.value && !isFetching.value) {
        execute()
      }
    }
    window.addEventListener('focus', handleFocus)
  }

  return {
    data: computed(() => data.value),
    error: computed(() => error.value),
    isLoading: computed(() => isLoading.value),
    isSuccess: computed(() => isSuccess.value),
    isError: computed(() => isError.value),
    isFetching: computed(() => isFetching.value),
    execute,
    refetch,
    reset,
  }
}
