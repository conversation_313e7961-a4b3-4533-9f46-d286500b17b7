<template>
  <div class="w-full">
    <label
      v-if="label"
      :for="inputId"
      class="block text-sm font-medium text-gray-700 mb-1"
    >
      {{ label }}
      <span v-if="required" class="text-red-500">*</span>
    </label>
    
    <div class="relative">
      <div
        v-if="$slots.prefix || prefixIcon"
        class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
      >
        <component
          v-if="prefixIcon"
          :is="prefixIcon"
          class="h-5 w-5 text-gray-400"
        />
        <slot name="prefix" />
      </div>
      
      <input
        :id="inputId"
        :type="type"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :required="required"
        :autocomplete="autocomplete"
        :class="inputClasses"
        @input="handleInput"
        @blur="handleBlur"
        @focus="handleFocus"
      />
      
      <div
        v-if="$slots.suffix || suffixIcon || showPasswordToggle"
        class="absolute inset-y-0 right-0 pr-3 flex items-center"
      >
        <button
          v-if="showPasswordToggle"
          type="button"
          class="text-gray-400 hover:text-gray-600 focus:outline-none"
          @click="togglePasswordVisibility"
        >
          <EyeIcon v-if="type === 'password'" class="h-5 w-5" />
          <EyeSlashIcon v-else class="h-5 w-5" />
        </button>
        <component
          v-else-if="suffixIcon"
          :is="suffixIcon"
          class="h-5 w-5 text-gray-400"
        />
        <slot name="suffix" />
      </div>
    </div>
    
    <p
      v-if="error"
      class="mt-1 text-sm text-red-600"
    >
      {{ error }}
    </p>
    
    <p
      v-else-if="hint"
      class="mt-1 text-sm text-gray-500"
    >
      {{ hint }}
    </p>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { EyeIcon, EyeSlashIcon } from '@heroicons/vue/24/outline'

interface Props {
  modelValue?: string | number
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url'
  label?: string
  placeholder?: string
  error?: string
  hint?: string
  disabled?: boolean
  readonly?: boolean
  required?: boolean
  autocomplete?: string
  prefixIcon?: any
  suffixIcon?: any
  size?: 'sm' | 'md' | 'lg'
  variant?: 'default' | 'filled'
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  disabled: false,
  readonly: false,
  required: false,
  size: 'md',
  variant: 'default',
})

const emit = defineEmits<{
  'update:modelValue': [value: string | number]
  blur: [event: FocusEvent]
  focus: [event: FocusEvent]
}>()

const inputId = `input-${Math.random().toString(36).substr(2, 9)}`
const currentType = ref(props.type)

const showPasswordToggle = computed(() => {
  return props.type === 'password' || currentType.value === 'password'
})

const baseInputClasses = 'block w-full rounded-lg border-gray-300 shadow-sm transition-colors duration-200 focus:border-primary-500 focus:ring-primary-500 disabled:bg-gray-50 disabled:text-gray-500'

const sizeClasses = computed(() => {
  switch (props.size) {
    case 'sm':
      return 'px-3 py-1.5 text-sm'
    case 'md':
      return 'px-3 py-2 text-sm'
    case 'lg':
      return 'px-4 py-3 text-base'
    default:
      return 'px-3 py-2 text-sm'
  }
})

const variantClasses = computed(() => {
  switch (props.variant) {
    case 'filled':
      return 'bg-gray-50 border-transparent focus:bg-white focus:border-primary-500'
    case 'default':
    default:
      return 'bg-white border-gray-300'
  }
})

const paddingClasses = computed(() => {
  const hasPrefix = !!props.prefixIcon || !!props.$slots?.prefix
  const hasSuffix = !!props.suffixIcon || !!props.$slots?.suffix || showPasswordToggle.value
  
  let classes = ''
  if (hasPrefix) classes += ' pl-10'
  if (hasSuffix) classes += ' pr-10'
  
  return classes
})

const inputClasses = computed(() => {
  return [
    baseInputClasses,
    sizeClasses.value,
    variantClasses.value,
    paddingClasses.value,
    props.error ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : '',
  ].join(' ')
})

const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement
  const value = props.type === 'number' ? Number(target.value) : target.value
  emit('update:modelValue', value)
}

const handleBlur = (event: FocusEvent) => {
  emit('blur', event)
}

const handleFocus = (event: FocusEvent) => {
  emit('focus', event)
}

const togglePasswordVisibility = () => {
  currentType.value = currentType.value === 'password' ? 'text' : 'password'
}
</script>
