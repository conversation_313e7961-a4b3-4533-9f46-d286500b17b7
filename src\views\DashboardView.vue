<template>
  <div>
    <!-- Page header -->
    <div class="md:flex md:items-center md:justify-between">
      <div class="flex-1 min-w-0">
        <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
          Dashboard
        </h2>
        <p class="mt-1 text-sm text-gray-500">
          Welcome back, {{ authStore.userDisplayName }}!
        </p>
      </div>
      <div class="mt-4 flex md:mt-0 md:ml-4">
        <BaseButton variant="primary">
          <PlusIcon class="w-5 h-5 mr-2" />
          New Report
        </BaseButton>
      </div>
    </div>

    <!-- Stats cards -->
    <div class="mt-8 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      <BaseCard
        v-for="stat in stats"
        :key="stat.name"
        class="overflow-hidden"
      >
        <div class="p-5">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <component
                :is="stat.icon"
                class="h-6 w-6 text-gray-400"
              />
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">
                  {{ stat.name }}
                </dt>
                <dd>
                  <div class="text-lg font-medium text-gray-900">
                    {{ stat.value }}
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-5 py-3">
          <div class="text-sm">
            <span
              :class="[
                stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600',
                'font-medium'
              ]"
            >
              {{ stat.change }}
            </span>
            <span class="text-gray-500"> from last month</span>
          </div>
        </div>
      </BaseCard>
    </div>

    <!-- Recent activity and charts -->
    <div class="mt-8 grid grid-cols-1 gap-8 lg:grid-cols-2">
      <!-- Recent Activity -->
      <BaseCard title="Recent Activity">
        <div class="flow-root">
          <ul class="-mb-8">
            <li
              v-for="(activity, index) in recentActivity"
              :key="activity.id"
            >
              <div class="relative pb-8">
                <span
                  v-if="index !== recentActivity.length - 1"
                  class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"
                ></span>
                <div class="relative flex space-x-3">
                  <div>
                    <span
                      :class="[
                        activity.iconBackground,
                        'h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white'
                      ]"
                    >
                      <component
                        :is="activity.icon"
                        class="h-5 w-5 text-white"
                      />
                    </span>
                  </div>
                  <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                    <div>
                      <p class="text-sm text-gray-500">
                        {{ activity.content }}
                      </p>
                    </div>
                    <div class="text-right text-sm whitespace-nowrap text-gray-500">
                      {{ activity.time }}
                    </div>
                  </div>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </BaseCard>

      <!-- Quick Actions -->
      <BaseCard title="Quick Actions">
        <div class="grid grid-cols-2 gap-4">
          <BaseButton
            v-for="action in quickActions"
            :key="action.name"
            variant="outline"
            class="h-20 flex-col"
            @click="action.action"
          >
            <component :is="action.icon" class="h-6 w-6 mb-2" />
            {{ action.name }}
          </BaseButton>
        </div>
      </BaseCard>
    </div>

    <!-- Charts section -->
    <div class="mt-8">
      <BaseCard title="Analytics Overview">
        <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
          <div class="text-center">
            <ChartBarIcon class="mx-auto h-12 w-12 text-gray-400" />
            <h3 class="mt-2 text-sm font-medium text-gray-900">Analytics Chart</h3>
            <p class="mt-1 text-sm text-gray-500">
              Chart component will be integrated here
            </p>
          </div>
        </div>
      </BaseCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { BaseButton, BaseCard } from '@/components/ui'
import {
  PlusIcon,
  UsersIcon,
  DocumentTextIcon,
  ChartBarIcon,
  CurrencyDollarIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  DocumentDuplicateIcon,
  UserPlusIcon,
  CogIcon,
} from '@heroicons/vue/24/outline'

const authStore = useAuthStore()

const stats = [
  {
    name: 'Total Users',
    value: '1,234',
    change: '+12%',
    changeType: 'increase',
    icon: UsersIcon,
  },
  {
    name: 'Active Reports',
    value: '89',
    change: '+5%',
    changeType: 'increase',
    icon: DocumentTextIcon,
  },
  {
    name: 'Revenue',
    value: '$45,678',
    change: '+8%',
    changeType: 'increase',
    icon: CurrencyDollarIcon,
  },
  {
    name: 'Conversion Rate',
    value: '3.24%',
    change: '-2%',
    changeType: 'decrease',
    icon: ChartBarIcon,
  },
]

const recentActivity = [
  {
    id: 1,
    content: 'New user registration completed',
    time: '2 hours ago',
    icon: UserPlusIcon,
    iconBackground: 'bg-green-500',
  },
  {
    id: 2,
    content: 'Report "Q4 Analysis" was generated',
    time: '4 hours ago',
    icon: DocumentTextIcon,
    iconBackground: 'bg-blue-500',
  },
  {
    id: 3,
    content: 'System maintenance completed',
    time: '6 hours ago',
    icon: CheckCircleIcon,
    iconBackground: 'bg-green-500',
  },
  {
    id: 4,
    content: 'Warning: High server load detected',
    time: '8 hours ago',
    icon: ExclamationTriangleIcon,
    iconBackground: 'bg-yellow-500',
  },
]

const quickActions = [
  {
    name: 'Create Report',
    icon: DocumentDuplicateIcon,
    action: () => console.log('Create report'),
  },
  {
    name: 'Add User',
    icon: UserPlusIcon,
    action: () => console.log('Add user'),
  },
  {
    name: 'View Analytics',
    icon: ChartBarIcon,
    action: () => console.log('View analytics'),
  },
  {
    name: 'Settings',
    icon: CogIcon,
    action: () => console.log('Settings'),
  },
]
</script>
