import type { User as FirebaseUser } from 'firebase/auth'

export interface User {
  uid: string
  email: string | null
  displayName: string | null
  photoURL: string | null
  emailVerified: boolean
  phoneNumber: string | null
  providerId: string
  createdAt?: string
  lastLoginAt?: string
}

export interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  accessToken: string | null
  refreshToken: string | null
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface RegisterCredentials {
  email: string
  password: string
  displayName?: string
}

export interface ResetPasswordData {
  email: string
}

export interface AuthError {
  code: string
  message: string
}

export type AuthProvider = 'email' | 'google' | 'apple'

export interface TokenData {
  accessToken: string
  refreshToken: string
  expiresIn: number
  tokenType: string
}

// Convert Firebase User to our User type
export const mapFirebaseUser = (firebaseUser: FirebaseUser): User => ({
  uid: firebaseUser.uid,
  email: firebaseUser.email,
  displayName: firebaseUser.displayName,
  photoURL: firebaseUser.photoURL,
  emailVerified: firebaseUser.emailVerified,
  phoneNumber: firebaseUser.phoneNumber,
  providerId: firebaseUser.providerData[0]?.providerId || 'email',
  createdAt: firebaseUser.metadata.creationTime,
  lastLoginAt: firebaseUser.metadata.lastSignInTime,
})
