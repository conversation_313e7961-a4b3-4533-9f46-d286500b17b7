<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Reset your password
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          Enter your email address and we'll send you a link to reset your password.
        </p>
      </div>
      
      <BaseCard class="mt-8">
        <div v-if="emailSent" class="text-center">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
            <CheckIcon class="h-6 w-6 text-green-600" />
          </div>
          <h3 class="mt-4 text-lg font-medium text-gray-900">Check your email</h3>
          <p class="mt-2 text-sm text-gray-600">
            We've sent a password reset link to <strong>{{ form.email }}</strong>
          </p>
          <div class="mt-6">
            <BaseButton
              variant="outline"
              full-width
              @click="resetForm"
            >
              Send another email
            </BaseButton>
          </div>
          <div class="mt-4">
            <router-link
              to="/auth/signin"
              class="text-sm font-medium text-primary-600 hover:text-primary-500"
            >
              Back to sign in
            </router-link>
          </div>
        </div>

        <form v-else class="space-y-6" @submit.prevent="handleSubmit">
          <div v-if="authStore.error" class="bg-red-50 border border-red-200 rounded-md p-4">
            <div class="flex">
              <ExclamationTriangleIcon class="h-5 w-5 text-red-400" />
              <div class="ml-3">
                <p class="text-sm text-red-800">{{ authStore.error }}</p>
              </div>
            </div>
          </div>

          <BaseInput
            v-model="form.email"
            type="email"
            label="Email address"
            placeholder="Enter your email"
            required
            autocomplete="email"
            :error="errors.email"
            :prefix-icon="EnvelopeIcon"
          />

          <BaseButton
            type="submit"
            variant="primary"
            size="lg"
            full-width
            :loading="authStore.isLoading"
            loading-text="Sending reset link..."
          >
            Send reset link
          </BaseButton>

          <div class="text-center">
            <router-link
              to="/auth/signin"
              class="text-sm font-medium text-primary-600 hover:text-primary-500"
            >
              Back to sign in
            </router-link>
          </div>
        </form>
      </BaseCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { BaseButton, BaseInput, BaseCard } from '@/components/ui'
import { 
  EnvelopeIcon, 
  ExclamationTriangleIcon,
  CheckIcon 
} from '@heroicons/vue/24/outline'

const authStore = useAuthStore()

const emailSent = ref(false)

const form = reactive({
  email: '',
})

const errors = reactive({
  email: '',
})

const validateForm = () => {
  errors.email = ''

  if (!form.email) {
    errors.email = 'Email is required'
    return false
  }

  if (!/\S+@\S+\.\S+/.test(form.email)) {
    errors.email = 'Please enter a valid email address'
    return false
  }

  return true
}

const handleSubmit = async () => {
  if (!validateForm()) return

  authStore.clearError()

  try {
    await authStore.resetPassword({ email: form.email })
    emailSent.value = true
  } catch (error) {
    // Error is handled by the store
  }
}

const resetForm = () => {
  emailSent.value = false
  form.email = ''
  errors.email = ''
  authStore.clearError()
}
</script>
