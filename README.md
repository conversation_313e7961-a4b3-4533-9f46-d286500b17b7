# Analyst Web - Vue.js Firebase Authentication App

A modern, scalable Vue.js application with Firebase authentication, TypeScript, and Tailwind CSS. This application provides a complete authentication system with protected routes, user management, and a responsive dashboard interface.

## 🚀 Features

- **Authentication System**
  - Email/Password sign-in and sign-up
  - Google OAuth integration
  - Apple OAuth integration
  - Password reset functionality
  - Secure token management with automatic refresh

- **User Interface**
  - Responsive design with Tailwind CSS
  - Left sidebar navigation
  - Top bar with user dropdown
  - Dashboard with analytics overview
  - User profile management
  - Modern component library

- **Technical Features**
  - TypeScript for type safety
  - Pinia for state management
  - Vue Router with authentication guards
  - Protected routes
  - API service layer with bearer token authentication
  - Secure token storage (cookies + localStorage)
  - Automatic token refresh
  - Error handling and loading states

## 🛠️ Tech Stack

- **Frontend**: Vue.js 3 (Composition API)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **State Management**: Pinia
- **Routing**: Vue Router
- **Authentication**: Firebase Auth
- **Icons**: Heroicons
- **Build Tool**: Vite
- **Package Manager**: npm

## 📋 Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- Firebase project with Authentication enabled

## 🔧 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd analyst_web
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure Firebase**
   - Create a Firebase project at [Firebase Console](https://console.firebase.google.com/)
   - Enable Authentication and configure providers:
     - Email/Password
     - Google (optional)
     - Apple (optional)
   - Get your Firebase configuration

4. **Environment Setup**
   - Copy `.env.example` to `.env`
   - Fill in your Firebase configuration:
   ```env
   VITE_FIREBASE_API_KEY=your_firebase_api_key
   VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
   VITE_FIREBASE_PROJECT_ID=your_project_id
   VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
   VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
   VITE_FIREBASE_APP_ID=your_app_id
   VITE_API_BASE_URL=http://localhost:3000/api
   ```

## 🚀 Development

### Start Development Server
```bash
npm run dev
```

### Build for Production
```bash
npm run build
```

### Preview Production Build
```bash
npm run preview
```

### Type Checking
```bash
npm run type-check
```

### Linting
```bash
npm run lint
```

### Testing
```bash
npm run test:unit
```

## 📁 Project Structure

```
src/
├── assets/              # Static assets
├── components/          # Reusable components
│   ├── layout/         # Layout components
│   └── ui/             # UI components
├── composables/        # Vue composables
├── config/             # Configuration files
├── layouts/            # Page layouts
├── router/             # Vue Router configuration
├── services/           # API and external services
├── stores/             # Pinia stores
├── types/              # TypeScript type definitions
└── views/              # Page components
    └── auth/           # Authentication pages
```

## 🔐 Authentication Flow

1. **Sign Up**: Users can create accounts with email/password or social providers
2. **Sign In**: Authentication with email/password or social providers
3. **Token Management**: Automatic token refresh and secure storage
4. **Protected Routes**: Routes require authentication to access
5. **Sign Out**: Secure logout with token cleanup

## 🎨 UI Components

The application includes a comprehensive set of reusable UI components:

- `BaseButton` - Customizable button component
- `BaseInput` - Form input with validation
- `BaseModal` - Modal dialog component
- `BaseCard` - Card container component
- `LoadingSpinner` - Loading indicator

## 🔒 Security Features

- Secure token storage using httpOnly cookies and localStorage
- Automatic token refresh before expiration
- Protected routes with authentication guards
- CSRF protection ready
- Input validation and sanitization

## 🌐 API Integration

The application is designed to work with a backend API:

- Bearer token authentication
- Automatic token refresh
- Error handling and retry logic
- Request/response interceptors
- Type-safe API calls

## 📱 Responsive Design

- Mobile-first approach
- Responsive sidebar navigation
- Adaptive layouts for all screen sizes
- Touch-friendly interface

## 🔧 Configuration

### Firebase Setup
1. Enable Authentication in Firebase Console
2. Configure sign-in methods
3. Add your domain to authorized domains
4. Update Firebase configuration in `.env`

### API Configuration
Update `src/config/api.ts` to match your backend API endpoints.

## 🚀 Deployment

### Build for Production
```bash
npm run build
```

### Deploy to Firebase Hosting (Optional)
```bash
npm install -g firebase-tools
firebase login
firebase init hosting
firebase deploy
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Check the documentation
- Open an issue on GitHub
- Contact the development team

## 🔄 Updates

This application is designed to be easily extensible. Future updates may include:
- Two-factor authentication
- Advanced user roles and permissions
- Real-time notifications
- Advanced analytics dashboard
- Mobile app integration
