export const apiConfig = {
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api',
  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT || '10000'),
  headers: {
    'Content-Type': 'application/json',
  },
}

export const endpoints = {
  auth: {
    profile: '/auth/profile',
    refresh: '/auth/refresh',
  },
  user: {
    profile: '/user/profile',
    update: '/user/profile',
  },
  // Add more endpoints as needed
} as const
