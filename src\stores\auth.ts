import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signInWithPopup,
  signOut,
  sendPasswordResetEmail,
  onAuthStateChanged,
  updateProfile,
  type User as FirebaseUser,
} from 'firebase/auth'
import { auth, googleProvider, appleProvider } from '@/config/firebase'
import type {
  AuthState,
  User,
  LoginCredentials,
  RegisterCredentials,
  ResetPasswordData,
  AuthProvider,
  TokenData,
} from '@/types/auth'
import { mapFirebaseUser } from '@/types/auth'
import { useTokenStorage } from '@/composables/useTokenStorage'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<User | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const accessToken = ref<string | null>(null)
  const refreshToken = ref<string | null>(null)

  // Token storage composable
  const { saveTokens, getTokens, clearTokens } = useTokenStorage()

  // Computed
  const isAuthenticated = computed(() => !!user.value)
  const userDisplayName = computed(() => user.value?.displayName || user.value?.email || 'User')
  const userAvatar = computed(() => user.value?.photoURL || null)

  // Actions
  const setLoading = (loading: boolean) => {
    isLoading.value = loading
  }

  const setError = (errorMessage: string | null) => {
    error.value = errorMessage
  }

  const setUser = (userData: User | null) => {
    user.value = userData
  }

  const setTokens = (tokenData: TokenData | null) => {
    if (tokenData) {
      accessToken.value = tokenData.accessToken
      refreshToken.value = tokenData.refreshToken
      saveTokens(tokenData)
    } else {
      accessToken.value = null
      refreshToken.value = null
      clearTokens()
    }
  }

  // Initialize auth state from stored tokens
  const initializeAuth = async () => {
    setLoading(true)
    try {
      const storedTokens = getTokens()
      if (storedTokens) {
        accessToken.value = storedTokens.accessToken
        refreshToken.value = storedTokens.refreshToken
      }

      // Listen for auth state changes
      onAuthStateChanged(auth, async (firebaseUser: FirebaseUser | null) => {
        if (firebaseUser) {
          const userData = mapFirebaseUser(firebaseUser)
          setUser(userData)
          
          // Get fresh token
          try {
            const token = await firebaseUser.getIdToken()
            const refreshToken = firebaseUser.refreshToken
            setTokens({
              accessToken: token,
              refreshToken,
              expiresIn: 3600,
              tokenType: 'Bearer',
            })
          } catch (tokenError) {
            console.error('Error getting token:', tokenError)
          }
        } else {
          setUser(null)
          setTokens(null)
        }
        setLoading(false)
      })
    } catch (err) {
      console.error('Auth initialization error:', err)
      setError('Failed to initialize authentication')
      setLoading(false)
    }
  }

  // Login with email and password
  const login = async (credentials: LoginCredentials) => {
    setLoading(true)
    setError(null)
    
    try {
      const userCredential = await signInWithEmailAndPassword(
        auth,
        credentials.email,
        credentials.password
      )
      
      const userData = mapFirebaseUser(userCredential.user)
      setUser(userData)
      
      const token = await userCredential.user.getIdToken()
      setTokens({
        accessToken: token,
        refreshToken: userCredential.user.refreshToken,
        expiresIn: 3600,
        tokenType: 'Bearer',
      })
      
      return userData
    } catch (err: any) {
      const errorMessage = getAuthErrorMessage(err.code)
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  // Register with email and password
  const register = async (credentials: RegisterCredentials) => {
    setLoading(true)
    setError(null)
    
    try {
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        credentials.email,
        credentials.password
      )
      
      // Update profile if displayName provided
      if (credentials.displayName) {
        await updateProfile(userCredential.user, {
          displayName: credentials.displayName,
        })
      }
      
      const userData = mapFirebaseUser(userCredential.user)
      setUser(userData)
      
      const token = await userCredential.user.getIdToken()
      setTokens({
        accessToken: token,
        refreshToken: userCredential.user.refreshToken,
        expiresIn: 3600,
        tokenType: 'Bearer',
      })
      
      return userData
    } catch (err: any) {
      const errorMessage = getAuthErrorMessage(err.code)
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  // Social login
  const loginWithProvider = async (provider: AuthProvider) => {
    setLoading(true)
    setError(null)
    
    try {
      let authProvider
      switch (provider) {
        case 'google':
          authProvider = googleProvider
          break
        case 'apple':
          authProvider = appleProvider
          break
        default:
          throw new Error('Unsupported provider')
      }
      
      const userCredential = await signInWithPopup(auth, authProvider)
      const userData = mapFirebaseUser(userCredential.user)
      setUser(userData)
      
      const token = await userCredential.user.getIdToken()
      setTokens({
        accessToken: token,
        refreshToken: userCredential.user.refreshToken,
        expiresIn: 3600,
        tokenType: 'Bearer',
      })
      
      return userData
    } catch (err: any) {
      const errorMessage = getAuthErrorMessage(err.code)
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  // Reset password
  const resetPassword = async (data: ResetPasswordData) => {
    setLoading(true)
    setError(null)
    
    try {
      await sendPasswordResetEmail(auth, data.email)
    } catch (err: any) {
      const errorMessage = getAuthErrorMessage(err.code)
      setError(errorMessage)
      throw new Error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  // Logout
  const logout = async () => {
    setLoading(true)
    try {
      await signOut(auth)
      setUser(null)
      setTokens(null)
    } catch (err: any) {
      console.error('Logout error:', err)
      setError('Failed to logout')
    } finally {
      setLoading(false)
    }
  }

  // Refresh token
  const refreshAuthToken = async () => {
    if (auth.currentUser) {
      try {
        const token = await auth.currentUser.getIdToken(true)
        setTokens({
          accessToken: token,
          refreshToken: auth.currentUser.refreshToken,
          expiresIn: 3600,
          tokenType: 'Bearer',
        })
        return token
      } catch (err) {
        console.error('Token refresh error:', err)
        throw err
      }
    }
    throw new Error('No authenticated user')
  }

  // Clear error
  const clearError = () => {
    setError(null)
  }

  return {
    // State
    user,
    isLoading,
    error,
    accessToken,
    refreshToken,
    
    // Computed
    isAuthenticated,
    userDisplayName,
    userAvatar,
    
    // Actions
    initializeAuth,
    login,
    register,
    loginWithProvider,
    resetPassword,
    logout,
    refreshAuthToken,
    clearError,
  }
})

// Helper function to get user-friendly error messages
function getAuthErrorMessage(errorCode: string): string {
  switch (errorCode) {
    case 'auth/user-not-found':
      return 'No account found with this email address.'
    case 'auth/wrong-password':
      return 'Incorrect password.'
    case 'auth/email-already-in-use':
      return 'An account with this email already exists.'
    case 'auth/weak-password':
      return 'Password should be at least 6 characters.'
    case 'auth/invalid-email':
      return 'Invalid email address.'
    case 'auth/user-disabled':
      return 'This account has been disabled.'
    case 'auth/too-many-requests':
      return 'Too many failed attempts. Please try again later.'
    case 'auth/popup-closed-by-user':
      return 'Sign-in popup was closed before completion.'
    case 'auth/cancelled-popup-request':
      return 'Sign-in was cancelled.'
    default:
      return 'An error occurred during authentication.'
  }
}
