<template>
  <div>
    <div class="md:flex md:items-center md:justify-between">
      <div class="flex-1 min-w-0">
        <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
          Analytics
        </h2>
        <p class="mt-1 text-sm text-gray-500">
          View detailed analytics and insights
        </p>
      </div>
    </div>

    <div class="mt-8">
      <BaseCard>
        <div class="text-center py-12">
          <ChartBarIcon class="mx-auto h-12 w-12 text-gray-400" />
          <h3 class="mt-2 text-sm font-medium text-gray-900">Analytics Dashboard</h3>
          <p class="mt-1 text-sm text-gray-500">
            Analytics features will be implemented here
          </p>
        </div>
      </BaseCard>
    </div>
  </div>
</template>

<script setup lang="ts">
import { BaseCard } from '@/components/ui'
import { ChartBarIcon } from '@heroicons/vue/24/outline'
</script>
