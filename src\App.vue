<script setup lang="ts">
import { onMounted } from 'vue'
import { RouterView } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { LoadingSpinner } from '@/components/ui'

const authStore = useAuthStore()

onMounted(async () => {
  // Initialize authentication on app startup
  await authStore.initializeAuth()
})
</script>

<template>
  <div id="app">
    <!-- Global loading state -->
    <div
      v-if="authStore.isLoading"
      class="fixed inset-0 bg-white bg-opacity-75 flex items-center justify-center z-50"
    >
      <div class="text-center">
        <LoadingSpinner size="xl" color="primary" />
        <p class="mt-4 text-gray-600">Loading...</p>
      </div>
    </div>

    <!-- Main app content -->
    <RouterView v-else />
  </div>
</template>

<style>
#app {
  min-height: 100vh;
}
</style>
