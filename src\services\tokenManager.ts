import { auth } from '@/config/firebase'
import { useTokenStorage } from '@/composables/useTokenStorage'
import type { TokenData } from '@/types/auth'

class TokenManager {
  private refreshPromise: Promise<string> | null = null
  private refreshTimer: NodeJS.Timeout | null = null
  private readonly TOKEN_REFRESH_THRESHOLD = 5 * 60 * 1000 // 5 minutes before expiry

  constructor() {
    this.setupAutoRefresh()
  }

  /**
   * Get a valid access token, refreshing if necessary
   */
  async getValidToken(): Promise<string | null> {
    try {
      if (!auth.currentUser) {
        return null
      }

      // Check if token needs refresh
      const tokenResult = await auth.currentUser.getIdTokenResult()
      const expirationTime = new Date(tokenResult.expirationTime).getTime()
      const now = Date.now()
      const timeUntilExpiry = expirationTime - now

      // If token expires soon, refresh it
      if (timeUntilExpiry <= this.TOKEN_REFRESH_THRESHOLD) {
        return this.refreshToken()
      }

      return tokenResult.token
    } catch (error) {
      console.error('Error getting valid token:', error)
      return null
    }
  }

  /**
   * Force refresh the current token
   */
  async refreshToken(): Promise<string> {
    // If a refresh is already in progress, wait for it
    if (this.refreshPromise) {
      return this.refreshPromise
    }

    this.refreshPromise = this.performTokenRefresh()

    try {
      const token = await this.refreshPromise
      return token
    } finally {
      this.refreshPromise = null
    }
  }

  /**
   * Perform the actual token refresh
   */
  private async performTokenRefresh(): Promise<string> {
    if (!auth.currentUser) {
      throw new Error('No authenticated user')
    }

    try {
      // Force refresh the token
      const token = await auth.currentUser.getIdToken(true)
      const refreshToken = auth.currentUser.refreshToken

      // Update stored tokens
      const { saveTokens } = useTokenStorage()
      const tokenData: TokenData = {
        accessToken: token,
        refreshToken,
        expiresIn: 3600, // 1 hour
        tokenType: 'Bearer',
      }
      saveTokens(tokenData)

      // Schedule next refresh
      this.scheduleTokenRefresh()

      return token
    } catch (error) {
      console.error('Token refresh failed:', error)
      throw error
    }
  }

  /**
   * Setup automatic token refresh
   */
  private setupAutoRefresh(): void {
    // Listen for auth state changes
    auth.onAuthStateChanged((user) => {
      if (user) {
        this.scheduleTokenRefresh()
      } else {
        this.clearRefreshTimer()
      }
    })
  }

  /**
   * Schedule the next token refresh
   */
  private async scheduleTokenRefresh(): Promise<void> {
    this.clearRefreshTimer()

    try {
      if (!auth.currentUser) {
        return
      }

      const tokenResult = await auth.currentUser.getIdTokenResult()
      const expirationTime = new Date(tokenResult.expirationTime).getTime()
      const now = Date.now()
      const timeUntilRefresh = expirationTime - now - this.TOKEN_REFRESH_THRESHOLD

      if (timeUntilRefresh > 0) {
        this.refreshTimer = setTimeout(() => {
          this.refreshToken().catch((error) => {
            console.error('Scheduled token refresh failed:', error)
            // Retry after a shorter interval on failure
            setTimeout(() => this.scheduleTokenRefresh(), 60000) // 1 minute
          })
        }, timeUntilRefresh)
      } else {
        // Token is already expired or about to expire, refresh immediately
        this.refreshToken().catch((error) => {
          console.error('Immediate token refresh failed:', error)
        })
      }
    } catch (error) {
      console.error('Error scheduling token refresh:', error)
    }
  }

  /**
   * Clear the refresh timer
   */
  private clearRefreshTimer(): void {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer)
      this.refreshTimer = null
    }
  }

  /**
   * Check if the current token is expired
   */
  async isTokenExpired(): Promise<boolean> {
    try {
      if (!auth.currentUser) {
        return true
      }

      const tokenResult = await auth.currentUser.getIdTokenResult()
      const expirationTime = new Date(tokenResult.expirationTime).getTime()
      return Date.now() >= expirationTime
    } catch (error) {
      console.error('Error checking token expiration:', error)
      return true
    }
  }

  /**
   * Get token expiration time
   */
  async getTokenExpirationTime(): Promise<number | null> {
    try {
      if (!auth.currentUser) {
        return null
      }

      const tokenResult = await auth.currentUser.getIdTokenResult()
      return new Date(tokenResult.expirationTime).getTime()
    } catch (error) {
      console.error('Error getting token expiration time:', error)
      return null
    }
  }

  /**
   * Revoke all tokens and clear storage
   */
  async revokeTokens(): Promise<void> {
    try {
      this.clearRefreshTimer()
      
      const { clearTokens } = useTokenStorage()
      clearTokens()

      // Sign out from Firebase
      if (auth.currentUser) {
        await auth.signOut()
      }
    } catch (error) {
      console.error('Error revoking tokens:', error)
      throw error
    }
  }

  /**
   * Get time until token expires (in milliseconds)
   */
  async getTimeUntilExpiry(): Promise<number | null> {
    const expirationTime = await this.getTokenExpirationTime()
    if (!expirationTime) {
      return null
    }
    return Math.max(0, expirationTime - Date.now())
  }

  /**
   * Check if token needs refresh soon
   */
  async needsRefresh(): Promise<boolean> {
    const timeUntilExpiry = await this.getTimeUntilExpiry()
    if (!timeUntilExpiry) {
      return true
    }
    return timeUntilExpiry <= this.TOKEN_REFRESH_THRESHOLD
  }
}

// Create and export singleton instance
export const tokenManager = new TokenManager()

// Composable for using token manager in components
export function useTokenManager() {
  return {
    getValidToken: () => tokenManager.getValidToken(),
    refreshToken: () => tokenManager.refreshToken(),
    isTokenExpired: () => tokenManager.isTokenExpired(),
    getTokenExpirationTime: () => tokenManager.getTokenExpirationTime(),
    revokeTokens: () => tokenManager.revokeTokens(),
    getTimeUntilExpiry: () => tokenManager.getTimeUntilExpiry(),
    needsRefresh: () => tokenManager.needsRefresh(),
  }
}

export default tokenManager
