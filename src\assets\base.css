/* Reset and base styles for Tailwind compatibility */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  line-height: 1.6;
  -webkit-text-size-adjust: 100%;
  font-family: Inter, system-ui, sans-serif;
}

body {
  margin: 0;
  min-height: 100vh;
  font-family: inherit;
  font-size: 16px;
  line-height: inherit;
  color: inherit;
  background-color: inherit;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Remove default button styles */
button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  font: inherit;
  cursor: pointer;
}

/* Remove default input styles */
input, textarea, select {
  font: inherit;
  color: inherit;
}

/* Remove default link styles */
a {
  color: inherit;
  text-decoration: none;
}

/* Remove default list styles */
ul, ol {
  list-style: none;
  padding: 0;
  margin: 0;
}
