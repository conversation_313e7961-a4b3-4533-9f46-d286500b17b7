<template>
  <button
    :type="type"
    :disabled="disabled || loading"
    :class="buttonClasses"
    @click="$emit('click', $event)"
  >
    <div v-if="loading" class="flex items-center">
      <LoadingSpinner :size="spinnerSize" class="mr-2" />
      <span>{{ loadingText || 'Loading...' }}</span>
    </div>
    <div v-else class="flex items-center justify-center">
      <component
        v-if="icon && iconPosition === 'left'"
        :is="icon"
        :class="iconClasses"
      />
      <slot />
      <component
        v-if="icon && iconPosition === 'right'"
        :is="icon"
        :class="iconClasses"
      />
    </div>
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import LoadingSpinner from './LoadingSpinner.vue'

interface Props {
  variant?: 'primary' | 'secondary' | 'danger' | 'ghost' | 'outline'
  size?: 'sm' | 'md' | 'lg'
  type?: 'button' | 'submit' | 'reset'
  disabled?: boolean
  loading?: boolean
  loadingText?: string
  icon?: any
  iconPosition?: 'left' | 'right'
  fullWidth?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'primary',
  size: 'md',
  type: 'button',
  disabled: false,
  loading: false,
  iconPosition: 'left',
  fullWidth: false,
})

defineEmits<{
  click: [event: MouseEvent]
}>()

const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed'

const variantClasses = computed(() => {
  switch (props.variant) {
    case 'primary':
      return 'bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500'
    case 'secondary':
      return 'bg-gray-200 hover:bg-gray-300 text-gray-900 focus:ring-gray-500'
    case 'danger':
      return 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-500'
    case 'ghost':
      return 'bg-transparent hover:bg-gray-100 text-gray-700 focus:ring-gray-500'
    case 'outline':
      return 'border border-gray-300 bg-transparent hover:bg-gray-50 text-gray-700 focus:ring-gray-500'
    default:
      return 'bg-primary-600 hover:bg-primary-700 text-white focus:ring-primary-500'
  }
})

const sizeClasses = computed(() => {
  switch (props.size) {
    case 'sm':
      return 'px-3 py-1.5 text-sm'
    case 'md':
      return 'px-4 py-2 text-sm'
    case 'lg':
      return 'px-6 py-3 text-base'
    default:
      return 'px-4 py-2 text-sm'
  }
})

const buttonClasses = computed(() => {
  return [
    baseClasses,
    variantClasses.value,
    sizeClasses.value,
    props.fullWidth ? 'w-full' : '',
  ].join(' ')
})

const iconClasses = computed(() => {
  const baseIconClasses = 'flex-shrink-0'
  switch (props.size) {
    case 'sm':
      return `${baseIconClasses} w-4 h-4`
    case 'md':
      return `${baseIconClasses} w-5 h-5`
    case 'lg':
      return `${baseIconClasses} w-6 h-6`
    default:
      return `${baseIconClasses} w-5 h-5`
  }
})

const spinnerSize = computed(() => {
  switch (props.size) {
    case 'sm':
      return 'sm'
    case 'md':
      return 'md'
    case 'lg':
      return 'lg'
    default:
      return 'md'
  }
})
</script>
