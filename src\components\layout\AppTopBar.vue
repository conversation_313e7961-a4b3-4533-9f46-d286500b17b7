<template>
  <div class="relative z-10 flex-shrink-0 flex h-16 bg-white shadow">
    <!-- Mobile menu button -->
    <button
      type="button"
      class="px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 md:hidden"
      @click="$emit('toggleSidebar')"
    >
      <Bars3Icon class="h-6 w-6" />
    </button>
    
    <!-- Desktop sidebar spacer -->
    <div class="flex-1 px-4 flex justify-between md:pl-64">
      <div class="flex-1 flex">
        <!-- Search bar -->
        <form class="w-full flex md:ml-0" action="#" method="GET">
          <label for="search-field" class="sr-only">Search</label>
          <div class="relative w-full text-gray-400 focus-within:text-gray-600">
            <div class="absolute inset-y-0 left-0 flex items-center pointer-events-none">
              <MagnifyingGlassIcon class="h-5 w-5" />
            </div>
            <input
              id="search-field"
              class="block w-full h-full pl-8 pr-3 py-2 border-transparent text-gray-900 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-0 focus:border-transparent"
              placeholder="Search..."
              type="search"
              name="search"
            />
          </div>
        </form>
      </div>
      
      <div class="ml-4 flex items-center md:ml-6">
        <!-- Notifications button -->
        <button
          type="button"
          class="bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          <BellIcon class="h-6 w-6" />
        </button>

        <!-- Profile dropdown -->
        <div class="ml-3 relative">
          <div>
            <button
              type="button"
              class="max-w-xs bg-white flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              @click="showUserMenu = !showUserMenu"
            >
              <img
                class="h-8 w-8 rounded-full"
                :src="authStore.userAvatar || defaultAvatar"
                :alt="authStore.userDisplayName"
              />
            </button>
          </div>
          
          <!-- User dropdown menu -->
          <Transition
            enter-active-class="transition ease-out duration-100"
            enter-from-class="transform opacity-0 scale-95"
            enter-to-class="transform opacity-100 scale-100"
            leave-active-class="transition ease-in duration-75"
            leave-from-class="transform opacity-100 scale-100"
            leave-to-class="transform opacity-0 scale-95"
          >
            <div
              v-if="showUserMenu"
              class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none"
              @click.away="showUserMenu = false"
            >
              <div class="px-4 py-3 border-b border-gray-100">
                <p class="text-sm font-medium text-gray-900">{{ authStore.userDisplayName }}</p>
                <p class="text-sm text-gray-500">{{ authStore.user?.email }}</p>
              </div>
              
              <router-link
                v-for="item in userNavigation"
                :key="item.name"
                :to="item.href"
                class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200"
                @click="showUserMenu = false"
              >
                <div class="flex items-center">
                  <component :is="item.icon" class="mr-3 h-4 w-4" />
                  {{ item.name }}
                </div>
              </router-link>
              
              <div class="border-t border-gray-100">
                <button
                  class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200"
                  @click="handleSignOut"
                >
                  <div class="flex items-center">
                    <ArrowRightOnRectangleIcon class="mr-3 h-4 w-4" />
                    Sign out
                  </div>
                </button>
              </div>
            </div>
          </Transition>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import {
  Bars3Icon,
  BellIcon,
  MagnifyingGlassIcon,
  UserIcon,
  CogIcon,
  ArrowRightOnRectangleIcon,
} from '@heroicons/vue/24/outline'

const router = useRouter()
const authStore = useAuthStore()

const showUserMenu = ref(false)

defineEmits<{
  toggleSidebar: []
}>()

const userNavigation = [
  { name: 'Your Profile', href: '/profile', icon: UserIcon },
  { name: 'Settings', href: '/settings', icon: CogIcon },
]

const defaultAvatar = 'https://ui-avatars.com/api/?name=' + encodeURIComponent(authStore.userDisplayName || 'User') + '&background=3b82f6&color=fff'

const handleSignOut = async () => {
  showUserMenu.value = false
  try {
    await authStore.logout()
    router.push('/auth/signin')
  } catch (error) {
    console.error('Sign out error:', error)
  }
}
</script>
