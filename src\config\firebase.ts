import { initializeApp } from 'firebase/app'
import { 
  getA<PERSON>, 
  GoogleAuthProvider, 
  OAuthProvider,
  connectAuthEmulator 
} from 'firebase/auth'

const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID
}

// Initialize Firebase
const app = initializeApp(firebaseConfig)

// Initialize Firebase Authentication and get a reference to the service
export const auth = getAuth(app)

// Configure providers
export const googleProvider = new GoogleAuthProvider()
googleProvider.addScope('email')
googleProvider.addScope('profile')

export const appleProvider = new OAuthProvider('apple.com')
appleProvider.addScope('email')
appleProvider.addScope('name')

// Connect to emulator in development
if (import.meta.env.VITE_APP_ENV === 'development' && !auth.config.emulator) {
  // Uncomment the line below if you want to use Firebase Auth emulator
  // connectAuthEmulator(auth, 'http://localhost:9099')
}

export default app
