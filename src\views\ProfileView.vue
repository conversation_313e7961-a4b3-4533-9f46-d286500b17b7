<template>
  <div>
    <!-- Page header -->
    <div class="md:flex md:items-center md:justify-between">
      <div class="flex-1 min-w-0">
        <h2 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
          Profile
        </h2>
        <p class="mt-1 text-sm text-gray-500">
          Manage your account settings and preferences
        </p>
      </div>
    </div>

    <div class="mt-8 grid grid-cols-1 gap-8 lg:grid-cols-3">
      <!-- Profile Information -->
      <div class="lg:col-span-2">
        <BaseCard title="Profile Information">
          <form @submit.prevent="handleUpdateProfile">
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
              <BaseInput
                v-model="profileForm.displayName"
                label="Display Name"
                placeholder="Enter your display name"
                :error="errors.displayName"
              />

              <BaseInput
                v-model="profileForm.email"
                type="email"
                label="Email Address"
                placeholder="Enter your email"
                disabled
                :error="errors.email"
                hint="Email cannot be changed"
              />

              <BaseInput
                v-model="profileForm.phoneNumber"
                type="tel"
                label="Phone Number"
                placeholder="Enter your phone number"
                :error="errors.phoneNumber"
              />

              <div class="sm:col-span-2">
                <label class="block text-sm font-medium text-gray-700 mb-1">
                  Bio
                </label>
                <textarea
                  v-model="profileForm.bio"
                  rows="4"
                  class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                  placeholder="Tell us about yourself..."
                ></textarea>
              </div>
            </div>

            <div class="mt-6 flex justify-end">
              <BaseButton
                type="submit"
                variant="primary"
                :loading="isUpdating"
                loading-text="Updating..."
              >
                Update Profile
              </BaseButton>
            </div>
          </form>
        </BaseCard>

        <!-- Security Settings -->
        <BaseCard title="Security Settings" class="mt-8">
          <div class="space-y-6">
            <div>
              <h4 class="text-sm font-medium text-gray-900">Password</h4>
              <p class="text-sm text-gray-500">
                Last changed {{ lastPasswordChange }}
              </p>
              <div class="mt-2">
                <BaseButton variant="outline" @click="showChangePassword = true">
                  Change Password
                </BaseButton>
              </div>
            </div>

            <div class="border-t border-gray-200 pt-6">
              <h4 class="text-sm font-medium text-gray-900">Two-Factor Authentication</h4>
              <p class="text-sm text-gray-500">
                Add an extra layer of security to your account
              </p>
              <div class="mt-2">
                <BaseButton variant="outline" disabled>
                  Enable 2FA (Coming Soon)
                </BaseButton>
              </div>
            </div>

            <div class="border-t border-gray-200 pt-6">
              <h4 class="text-sm font-medium text-gray-900">Connected Accounts</h4>
              <div class="mt-4 space-y-3">
                <div
                  v-for="provider in connectedProviders"
                  :key="provider.name"
                  class="flex items-center justify-between"
                >
                  <div class="flex items-center">
                    <component :is="provider.icon" class="h-5 w-5 mr-3" />
                    <span class="text-sm text-gray-900">{{ provider.name }}</span>
                  </div>
                  <span
                    :class="[
                      provider.connected ? 'text-green-600' : 'text-gray-400',
                      'text-sm'
                    ]"
                  >
                    {{ provider.connected ? 'Connected' : 'Not connected' }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </BaseCard>
      </div>

      <!-- Profile Picture and Quick Info -->
      <div class="lg:col-span-1">
        <BaseCard title="Profile Picture">
          <div class="text-center">
            <img
              class="mx-auto h-32 w-32 rounded-full"
              :src="authStore.userAvatar || defaultAvatar"
              :alt="authStore.userDisplayName"
            />
            <h3 class="mt-4 text-lg font-medium text-gray-900">
              {{ authStore.userDisplayName }}
            </h3>
            <p class="text-sm text-gray-500">{{ authStore.user?.email }}</p>
            
            <div class="mt-4">
              <BaseButton variant="outline" size="sm">
                Change Photo
              </BaseButton>
            </div>
          </div>
        </BaseCard>

        <!-- Account Stats -->
        <BaseCard title="Account Statistics" class="mt-8">
          <dl class="space-y-4">
            <div>
              <dt class="text-sm font-medium text-gray-500">Member since</dt>
              <dd class="text-sm text-gray-900">{{ memberSince }}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Last login</dt>
              <dd class="text-sm text-gray-900">{{ lastLogin }}</dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Email verified</dt>
              <dd class="text-sm text-gray-900">
                <span
                  :class="[
                    authStore.user?.emailVerified ? 'text-green-600' : 'text-red-600',
                    'inline-flex items-center'
                  ]"
                >
                  <component
                    :is="authStore.user?.emailVerified ? CheckCircleIcon : XCircleIcon"
                    class="h-4 w-4 mr-1"
                  />
                  {{ authStore.user?.emailVerified ? 'Verified' : 'Not verified' }}
                </span>
              </dd>
            </div>
          </dl>
        </BaseCard>
      </div>
    </div>

    <!-- Change Password Modal -->
    <BaseModal
      v-model="showChangePassword"
      title="Change Password"
      size="md"
    >
      <form @submit.prevent="handleChangePassword">
        <div class="space-y-4">
          <BaseInput
            v-model="passwordForm.currentPassword"
            type="password"
            label="Current Password"
            required
            :error="passwordErrors.currentPassword"
          />
          
          <BaseInput
            v-model="passwordForm.newPassword"
            type="password"
            label="New Password"
            required
            :error="passwordErrors.newPassword"
            hint="Password must be at least 6 characters"
          />
          
          <BaseInput
            v-model="passwordForm.confirmPassword"
            type="password"
            label="Confirm New Password"
            required
            :error="passwordErrors.confirmPassword"
          />
        </div>
      </form>

      <template #footer>
        <BaseButton
          variant="secondary"
          @click="showChangePassword = false"
        >
          Cancel
        </BaseButton>
        <BaseButton
          variant="primary"
          :loading="isChangingPassword"
          @click="handleChangePassword"
        >
          Change Password
        </BaseButton>
      </template>
    </BaseModal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { BaseButton, BaseCard, BaseInput, BaseModal } from '@/components/ui'
import {
  CheckCircleIcon,
  XCircleIcon,
} from '@heroicons/vue/24/outline'

const authStore = useAuthStore()

const isUpdating = ref(false)
const isChangingPassword = ref(false)
const showChangePassword = ref(false)

const profileForm = reactive({
  displayName: authStore.user?.displayName || '',
  email: authStore.user?.email || '',
  phoneNumber: authStore.user?.phoneNumber || '',
  bio: '',
})

const errors = reactive({
  displayName: '',
  email: '',
  phoneNumber: '',
})

const passwordForm = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: '',
})

const passwordErrors = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: '',
})

const connectedProviders = [
  { name: 'Google', icon: 'div', connected: authStore.user?.providerId === 'google.com' },
  { name: 'Apple', icon: 'div', connected: authStore.user?.providerId === 'apple.com' },
]

const defaultAvatar = computed(() => 
  'https://ui-avatars.com/api/?name=' + 
  encodeURIComponent(authStore.userDisplayName || 'User') + 
  '&background=3b82f6&color=fff'
)

const memberSince = computed(() => {
  if (authStore.user?.createdAt) {
    return new Date(authStore.user.createdAt).toLocaleDateString()
  }
  return 'Unknown'
})

const lastLogin = computed(() => {
  if (authStore.user?.lastLoginAt) {
    return new Date(authStore.user.lastLoginAt).toLocaleDateString()
  }
  return 'Unknown'
})

const lastPasswordChange = 'Never'

const handleUpdateProfile = async () => {
  isUpdating.value = true
  
  try {
    // Here you would call your API to update the profile
    console.log('Updating profile:', profileForm)
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Show success message
    alert('Profile updated successfully!')
  } catch (error) {
    console.error('Profile update error:', error)
    alert('Failed to update profile')
  } finally {
    isUpdating.value = false
  }
}

const handleChangePassword = async () => {
  // Reset errors
  Object.keys(passwordErrors).forEach(key => {
    passwordErrors[key as keyof typeof passwordErrors] = ''
  })

  // Validate
  if (!passwordForm.currentPassword) {
    passwordErrors.currentPassword = 'Current password is required'
    return
  }

  if (!passwordForm.newPassword) {
    passwordErrors.newPassword = 'New password is required'
    return
  }

  if (passwordForm.newPassword.length < 6) {
    passwordErrors.newPassword = 'Password must be at least 6 characters'
    return
  }

  if (passwordForm.newPassword !== passwordForm.confirmPassword) {
    passwordErrors.confirmPassword = 'Passwords do not match'
    return
  }

  isChangingPassword.value = true

  try {
    // Here you would call your API to change the password
    console.log('Changing password')
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    showChangePassword.value = false
    alert('Password changed successfully!')
    
    // Reset form
    Object.keys(passwordForm).forEach(key => {
      passwordForm[key as keyof typeof passwordForm] = ''
    })
  } catch (error) {
    console.error('Password change error:', error)
    alert('Failed to change password')
  } finally {
    isChangingPassword.value = false
  }
}
</script>
